from typing import List, Literal, Optional
from beanie import Document
from pydantic import Field


# 用户
class User(Document):
    username: Optional[str] = Field(default=None, title="用户名")
    password: Optional[str] = Field(default=None, title="密码")
    roles: Optional[List[Literal["user", "admin"]]] = Field(
        default=["user"], title="用户角色列表"
    )
    create_at: Optional[int] = Field(default=None, title="创建时间(时间戳)")
    is_deleted: Optional[bool] = Field(default=None, title="删除标记")

    def to_dict(self):
        data = self.model_dump()
        data["_id"] = str(self.id)
        data.pop("id", None)
        return data

    class Settings:
        name = "user"


# 授权码
class AuthorizationCode(Document):
    code: Optional[str] = Field(default=None, title="授权码")
    status: Optional[Literal["未使用", "已使用"]] = Field(
        default="未使用", title="授权码状态"
    )
    create_at: Optional[int] = Field(default=None, title="创建时间(时间戳)")
    used_at: Optional[int] = Field(default=None, title="使用时间(时间戳)")
    user_id: Optional[str] = Field(default=None, title="关联的 User 的ID(使用者)")
    is_deleted: Optional[bool] = Field(default=None, title="删除标记")

    def to_dict(self):
        data = self.model_dump()
        data["_id"] = str(self.id)
        data.pop("id", None)
        return data

    class Settings:
        name = "authorization_code"


# 软著任务
class CopyrightTask(Document):
    user_id: Optional[str] = Field(default=None, title="关联的 User 的ID(创建用户)")
    name: Optional[str] = Field(default=None, title="任务名称")
    status: Optional[Literal["生成中", "已完成", "生成失败"]] = Field(
        default="生成中", title="任务状态"
    )
    description: Optional[str] = Field(default=None, title="需求描述")
    document_key: Optional[str] = Field(default=None, title="文档OSS存储键")
    deployment_url: Optional[str] = Field(default=None, title="部署URL")
    progress: Optional[int] = Field(default=0, title="生成进度")
    create_at: Optional[int] = Field(default=None, title="创建时间(时间戳)")
    completed_at: Optional[int] = Field(default=None, title="完成时间(时间戳)")
    is_deleted: Optional[bool] = Field(default=None, title="删除标记")

    def to_dict(self):
        data = self.model_dump()
        data["_id"] = str(self.id)
        data.pop("id", None)
        return data

    class Settings:
        name = "copyright_task"
