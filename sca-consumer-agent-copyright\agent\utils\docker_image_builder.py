import os
import posixpath
import random
from datetime import datetime, timedelta, timezone
from typing import <PERSON>ple

import paramiko

from config.config import DOCKER_CONFIG
from omni.log.log import olog
from agent.utils.path_utils import smart_join_path


def _connect_ssh(remote_config):
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

    connect_params = {
        "hostname": remote_config.get("host"),
        "port": remote_config.get("port", 22),
        "username": remote_config.get("username"),
        "password": remote_config.get("password")
    }

    ssh.connect(**connect_params)
    olog.debug(f"SSH连接成功: {remote_config.get('host')}")
    return ssh


def _command_execution(ssh_client, cmd):
    olog.debug(f"执行远程命令: {cmd}")

    stdin, stdout, stderr = ssh_client.exec_command(cmd)

    output_lines = []
    while not stdout.channel.exit_status_ready():
        if stdout.channel.recv_ready():
            line = stdout.channel.recv(1024).decode('utf-8').strip()
            if line:
                olog.debug(f"命令输出: {line}")
                output_lines.append(line)

    exit_code = stdout.channel.recv_exit_status()
    error = stderr.read().decode('utf-8').strip()
    output = "\n".join(output_lines) or stdout.read().decode('utf-8').strip()

    if exit_code != 0:
        return False, output, error

    olog.debug(f"远程命令执行成功 (退出码: {exit_code})")
    return True, output, None


def _get_available_port(ssh_client):
    port_range = DOCKER_CONFIG.get("port_range", {"start": 20000, "end": 30000})
    start_range = port_range.get("start", 20000)
    end_range = port_range.get("end", 30000)

    for _ in range(10):
        port = random.randint(start_range, end_range)
        cmd = f"nc -z -v -w5 127.0.0.1 {port} > /dev/null 2>&1 || echo 'available'"
        success, output, _ = _command_execution(ssh_client, cmd)

        if success and 'available' in output:
            olog.debug(f"找到远程可用端口: {port}")
            return port

    olog.warning("无法确认远程端口可用性，使用随机端口")
    return random.randint(start_range, end_range)


def _prepare_remote_dir(ssh_client, remote_build_dir):
    remove_cmd = f"rm -rf {remote_build_dir}"
    _command_execution(ssh_client, remove_cmd)
    olog.debug(f"删除远程目录: {remote_build_dir}")

    success, _, error = _command_execution(ssh_client, f"mkdir -p {remote_build_dir}")
    if not success:
        olog.error(f"创建远程目录失败: {error}")
        return False

    olog.debug(f"创建远程目录成功: {remote_build_dir}")
    return True


def _upload_project(ssh_client, project_path, remote_build_dir):
    olog.debug("正在准备上传项目文件")

    sftp = ssh_client.open_sftp()

    def upload_dir(local_dir, remote_dir):
        try:
            sftp.stat(remote_dir)
        except FileNotFoundError:
            sftp.mkdir(remote_dir)

        for item in os.listdir(local_dir):
            local_path = smart_join_path(local_dir, item)
            remote_path = posixpath.join(remote_dir, item)

            if os.path.isfile(local_path):
                sftp.put(local_path, remote_path)
            elif os.path.isdir(local_path):
                upload_dir(local_path, remote_path)

    upload_dir(project_path, remote_build_dir)

    dockerfile_src = DOCKER_CONFIG.get("dockerfile_path")
    parent_dir = posixpath.dirname(remote_build_dir)
    remote_dockerfile_path = posixpath.join(parent_dir, "Dockerfile")
    olog.debug(f"上传Dockerfile到: {remote_dockerfile_path}")
    sftp.put(dockerfile_src, remote_dockerfile_path)

    sftp.close()
    olog.debug("项目文件上传成功")
    return True


def _build_docker_image(ssh_client, docker_name, project_path, remote_build_dir):
    olog.debug(f"开始构建Docker镜像: {docker_name}")

    parent_dir = posixpath.dirname(remote_build_dir)

    build_cmd = f"cd {parent_dir} && docker build -t {docker_name} ."
    success, output, error = _command_execution(ssh_client, build_cmd)

    if not success:
        full_error_log = f"{error}\n{output}" if output else error
        return False, full_error_log

    check_cmd = f"docker image inspect {docker_name}"
    success, output, error = _command_execution(ssh_client, check_cmd)

    if not success:
        olog.exception(f"Docker镜像构建失败，未找到生成的镜像，错误: {error}")
        return False, f"构建过程完成但未找到镜像: {error}"

    olog.debug("Docker镜像远程构建成功")
    return True, None


def _start_container(ssh_client, docker_name, docker_port, remote_config):
    olog.debug(f"开始启动Docker容器: {docker_name}")

    network_name = f"{docker_name}-network"
    network_cmd = f"docker network inspect {network_name} || docker network create --driver bridge {network_name}"
    _command_execution(ssh_client, network_cmd)

    run_cmd = (
        f"docker run -d --name {docker_name} "
        f"-p {docker_port}:3000 "
        f"--network {network_name} "
        f"{docker_name}:latest"
    )

    success, output, error = _command_execution(ssh_client, run_cmd)

    if not success:
        olog.exception(f"启动Docker容器失败，详细错误: {error}")
        return False, f"启动失败: {error}"

    access_url = f"http://{remote_config.get('host')}:{docker_port}"
    olog.debug(f"Docker容器启动成功: {docker_name}")
    olog.debug(f"应用访问地址: {access_url}")
    return True, access_url


def run_docker_deployment(project_path, sandbox_dir_name):
    """
    运行Docker部署流程
    
    Args:
        project_path: 项目路径
        sandbox_dir_name: 项目名称，用于指定docker_name和remote_build_dir
    
    Returns:
        tuple: (成功标志, 访问URL, 容器名称, 错误信息)
    """
    olog.info(f"开始Docker部署流程")

    remote_config = DOCKER_CONFIG.get("remote", {})
    docker_name = sandbox_dir_name
    remote_build_dir = posixpath.join(
        remote_config.get("remote_dir", "/tmp/sca-remote"),
        sandbox_dir_name,
        "build"
    )

    ssh_client = _connect_ssh(remote_config)
    docker_port = _get_available_port(ssh_client)

    if not _prepare_remote_dir(ssh_client, remote_build_dir):
        return False, None, None, "无法创建远程目录"

    if not os.path.exists(project_path):
        return False, None, None, f"项目路径不存在: {project_path}"

    if not _upload_project(ssh_client, project_path, remote_build_dir):
        return False, None, None, "上传项目文件失败"

    build_success, build_error = _build_docker_image(ssh_client, docker_name, project_path, remote_build_dir)
    if not build_success:
        return False, None, None, build_error

    start_success, start_result = _start_container(ssh_client, docker_name, docker_port, remote_config)
    if not start_success:
        return False, None, None, start_result

    access_url = f"http://{remote_config.get('host')}:{docker_port}"

    olog.info("Docker部署流程完成")
    return True, access_url, docker_name, None


def stop_container_by_name(container_name):
    """
    仅通过容器名称停止Docker容器，不会删除容器
    
    Args:
        container_name: 容器名称
    
    Returns:
        tuple: (成功标志, 结果信息)
    """
    if not container_name:
        return False, "必须提供容器名称"

    olog.info(f"开始停止Docker容器: {container_name}")

    remote_config = DOCKER_CONFIG.get("remote", {})
    ssh_client = _connect_ssh(remote_config)

    check_cmd = f"docker ps -a --filter name=^/{container_name}$ --format '{{{{.ID}}}}'"
    success, output, error = _command_execution(ssh_client, check_cmd)

    container_id = output.strip() if success and output else ""

    if not container_id:
        message = f"远程容器 {container_name} 不存在，无需停止"
        olog.warning(message)
        return False, message

    olog.debug(f"停止远程容器: {container_name}")
    stop_cmd = f"docker stop {container_name}"
    success, output, error = _command_execution(ssh_client, stop_cmd)

    if not success:
        olog.exception(f"停止远程容器失败: {error}")
        return False, f"停止远程容器失败: {error}"

    olog.info(f"远程容器 {container_name} 已成功停止")
    return True, f"远程容器 {container_name} 已成功停止"


def remove_container_by_name(container_name):
    """
    通过容器名称删除Docker容器
    
    Args:
        container_name: 容器名称
    
    Returns:
        tuple: (成功标志, 结果信息)
    """
    olog.info(f"开始删除Docker容器: {container_name}")

    remote_config = DOCKER_CONFIG.get("remote", {})
    ssh_client = _connect_ssh(remote_config)

    check_cmd = f"docker ps -a --filter name=^/{container_name}$ --format '{{{{.ID}}}}'"
    success, output, error = _command_execution(ssh_client, check_cmd)

    container_id = output.strip() if success and output else ""

    if not container_id:
        message = f"远程容器 {container_name} 不存在，无需删除"
        olog.warning(message)
        return False, message

    olog.debug(f"停止并删除远程容器: {container_name}")

    # 先停止容器
    stop_cmd = f"docker stop {container_name}"
    _command_execution(ssh_client, stop_cmd)

    # 删除容器
    rm_cmd = f"docker rm {container_name}"
    success, output, error = _command_execution(ssh_client, rm_cmd)

    if not success:
        olog.exception(f"删除远程容器失败: {error}")
        return False, f"删除远程容器失败: {error}"

    # 删除相关网络
    network_name = f"{container_name}-network"
    network_cmd = f"docker network rm {network_name}"
    _command_execution(ssh_client, network_cmd)

    olog.info(f"远程容器 {container_name} 已成功删除")
    return True, f"远程容器 {container_name} 已成功删除"


def stop_old_sca_sandbox_containers() -> Tuple[bool, str]:
    """
    停止创建时间超过24小时且名称以 'sca-sandbox' 开头的Docker容器
    
    Returns:
        tuple: (成功标志, 结果信息)
    """
    olog.info("开始扫描并停止旧的沙箱容器")

    remote_config = DOCKER_CONFIG.get("remote", {})
    ssh_client = _connect_ssh(remote_config)

    try:
        get_ids_cmd = 'docker ps -q --filter "status=running" --filter "name=sca-sandbox*"'
        success, ids_str, error = _command_execution(ssh_client, get_ids_cmd)

        if not success:
            olog.error(f"获取正在运行的沙箱容器ID失败: {error}")
            return False, f"获取正在运行的沙箱容器ID失败: {error}"

        container_ids = ids_str.strip().split()
        if not container_ids:
            olog.info("没有正在运行的沙箱容器")
            return True, "没有正在运行的沙箱容器"

        olog.debug(f"找到 {len(container_ids)} 个正在运行的沙箱容器，ID: {', '.join(container_ids)}")

        inspect_cmd = f"docker inspect --format='{{{{.ID}}}}~~{{{{.Name}}}}~~{{{{.Created}}}}' {' '.join(container_ids)}"
        success, inspect_output, error = _command_execution(ssh_client, inspect_cmd)

        if not success:
            olog.error(f"检查容器详细信息失败: {error}")
            return False, f"检查容器详细信息失败: {error}"

        containers_to_stop = []
        now_utc = datetime.now(timezone.utc)

        for line in inspect_output.strip().split('\n'):
            if not line.strip():
                continue

            container_id, container_name, created_str = line.split('~~')
            container_name = container_name.lstrip('/')
            olog.debug(f"正在检查容器: {container_name} ({container_id})")

            created_str = created_str.strip()
            created_str_norm = created_str.rstrip('Z')
            if '.' in created_str_norm:
                ts_part, frac_part = created_str_norm.split('.', 1)
                created_str_norm = f"{ts_part}.{frac_part[:6]}"

            created_dt_naive = datetime.fromisoformat(created_str_norm)
            created_dt = created_dt_naive.replace(tzinfo=timezone.utc)

            if now_utc - created_dt > timedelta(hours=24):
                olog.debug(f"容器 {container_name} 创建于 {created_dt}，已超过24小时，将被停止")
                containers_to_stop.append({"id": container_id, "name": container_name})
            else:
                olog.debug(f"容器 {container_name} 创建于 {created_dt}，未超过24小时，将保留")

        if not containers_to_stop:
            olog.info("没有需要停止的旧沙箱容器")
            return True, "没有找到需要停止的旧沙箱容器"

        container_names_to_stop = [c["name"] for c in containers_to_stop]
        olog.info(f"发现 {len(container_names_to_stop)} 个需要停止的旧容器: {', '.join(container_names_to_stop)}")

        container_ids_to_stop = [c["id"] for c in containers_to_stop]

        stop_cmd = f"docker stop {' '.join(container_ids_to_stop)}"
        success, _, stop_error = _command_execution(ssh_client, stop_cmd)

        if not success:
            message = f"尝试停止 {len(containers_to_stop)} 个容器失败: {stop_error}"
            olog.error(message)
            return False, message

        message = f"成功停止了 {len(containers_to_stop)} 个旧容器"
        olog.info(message)
        return True, message
    finally:
        if ssh_client:
            ssh_client.close()
